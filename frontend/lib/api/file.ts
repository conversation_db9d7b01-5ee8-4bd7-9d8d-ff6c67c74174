// API client for file upload and download operations

const getApiBase = () => {
  if (typeof window !== 'undefined') {
    return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
  }
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
};

const API_BASE = getApiBase();

export interface PresignUploadRequest {
  submission_id: string;
  question_id: string;
  original_filename: string;
  mime_type: string;
  file_size: number;
}

export interface PresignUploadResponse {
  presigned_url: string;
  s3_key: string;
  file_id: string;
  expires_in: number;
  upload_fields: Record<string, string>;
}

export interface ConfirmUploadRequest {
  file_id: string;
  checksum?: string;
}

export interface ConfirmUploadResponse {
  file_id: string;
  s3_key: string;
  status: string;
  message: string;
}

export interface PresignDownloadRequest {
  file_id: string;
}

export interface PresignDownloadResponse {
  presigned_url: string;
  filename: string;
  file_size: number;
  mime_type: string;
  expires_in: number;
}

export interface FileInfo {
  id: string;
  original_filename: string;
  file_size: number;
  display_size: string;
  mime_type: string;
  file_extension: string;
  question_id: string;
  uploaded_at: number;
  uploaded_by: string;
  download_count: number;
  last_accessed_at?: number;
}

export interface FileUploadProgress {
  file_id: string;
  filename: string;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

export class FileAPI {
  /**
   * Generate presigned URL for file upload
   */
  static async generateUploadUrl(
    request: PresignUploadRequest,
    accessToken?: string
  ): Promise<PresignUploadResponse> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }

      const response = await fetch(`${API_BASE}/api/v1/public/file/presign-upload`, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to generate upload URL');
      }

      return response.json();
    } catch (error) {
      console.error('Generate upload URL error:', error);
      throw error;
    }
  }

  /**
   * Upload file to S3 using presigned URL
   */
  static async uploadFile(
    file: File,
    presignedUrl: string,
    uploadFields: Record<string, string>,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Track upload progress
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100;
            onProgress(progress);
          }
        });
      }

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve();
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      xhr.addEventListener('abort', () => {
        reject(new Error('Upload aborted'));
      });

      // Prepare form data for S3 upload
      const formData = new FormData();
      
      // Add upload fields
      Object.entries(uploadFields).forEach(([key, value]) => {
        formData.append(key, value);
      });
      
      // Add the file last
      formData.append('file', file);

      xhr.open('PUT', presignedUrl);
      xhr.setRequestHeader('Content-Type', file.type);
      xhr.setRequestHeader('Content-Length', file.size.toString());
      xhr.send(file);
    });
  }

  /**
   * Confirm file upload completion
   */
  static async confirmUpload(
    request: ConfirmUploadRequest,
    accessToken?: string
  ): Promise<ConfirmUploadResponse> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }

      const response = await fetch(`${API_BASE}/api/v1/public/file/confirm-upload`, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to confirm upload');
      }

      return response.json();
    } catch (error) {
      console.error('Confirm upload error:', error);
      throw error;
    }
  }

  /**
   * Generate presigned URL for file download
   */
  static async generateDownloadUrl(
    request: PresignDownloadRequest,
    accessToken?: string
  ): Promise<PresignDownloadResponse> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }

      const response = await fetch(`${API_BASE}/api/v1/public/file/presign-download`, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to generate download URL');
      }

      return response.json();
    } catch (error) {
      console.error('Generate download URL error:', error);
      throw error;
    }
  }

  /**
   * Delete a file
   */
  static async deleteFile(
    fileId: string,
    accessToken?: string
  ): Promise<{ message: string; file_id: string }> {
    try {
      const headers: Record<string, string> = {};

      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }

      const response = await fetch(`${API_BASE}/api/v1/public/file/${fileId}`, {
        method: 'DELETE',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to delete file');
      }

      return response.json();
    } catch (error) {
      console.error('Delete file error:', error);
      throw error;
    }
  }

  /**
   * List files for a submission
   */
  static async listSubmissionFiles(
    submissionId: string,
    questionId?: string,
    accessToken?: string
  ): Promise<FileInfo[]> {
    try {
      const headers: Record<string, string> = {};

      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }

      const url = new URL(`${API_BASE}/api/v1/public/file/submission/${submissionId}/files`);
      if (questionId) {
        url.searchParams.set('question_id', questionId);
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to list files');
      }

      return response.json();
    } catch (error) {
      console.error('List files error:', error);
      throw error;
    }
  }

  /**
   * Complete file upload process (generate URL, upload, confirm)
   */
  static async uploadFileComplete(
    file: File,
    submissionId: string,
    questionId: string,
    accessToken?: string,
    onProgress?: (progress: FileUploadProgress) => void
  ): Promise<ConfirmUploadResponse> {
    try {
      // Step 1: Generate presigned upload URL
      onProgress?.({
        file_id: '',
        filename: file.name,
        progress: 0,
        status: 'uploading'
      });

      const uploadRequest: PresignUploadRequest = {
        submission_id: submissionId,
        question_id: questionId,
        original_filename: file.name,
        mime_type: file.type,
        file_size: file.size
      };

      const uploadResponse = await this.generateUploadUrl(uploadRequest, accessToken);

      // Step 2: Upload file to S3
      await this.uploadFile(
        file,
        uploadResponse.presigned_url,
        uploadResponse.upload_fields,
        (progress) => {
          onProgress?.({
            file_id: uploadResponse.file_id,
            filename: file.name,
            progress,
            status: 'uploading'
          });
        }
      );

      // Step 3: Confirm upload
      onProgress?.({
        file_id: uploadResponse.file_id,
        filename: file.name,
        progress: 100,
        status: 'processing'
      });

      const confirmResponse = await this.confirmUpload(
        { file_id: uploadResponse.file_id },
        accessToken
      );

      onProgress?.({
        file_id: uploadResponse.file_id,
        filename: file.name,
        progress: 100,
        status: 'completed'
      });

      return confirmResponse;

    } catch (error) {
      onProgress?.({
        file_id: '',
        filename: file.name,
        progress: 0,
        status: 'error',
        error: error instanceof Error ? error.message : 'Upload failed'
      });
      throw error;
    }
  }
}
