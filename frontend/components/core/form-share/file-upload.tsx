"use client";

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FileText, Upload, Trash2, Download, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { FileAPI, FileUploadProgress, FileInfo } from '@/lib/api/file';
import { FileDisplay } from './file-display';

interface FileUploadProps {
  questionId: string;
  submissionId: string;
  value?: any;
  onChange: (value: any) => void;
  maxSizeMB?: number;
  allowedTypes?: string;
  accessToken?: string;
  error?: string;
}

export function FileUpload({
  questionId,
  submissionId,
  value,
  onChange,
  maxSizeMB = 10,
  allowedTypes = '.pdf,.doc,.docx,.jpg,.jpeg,.png',
  accessToken,
  error
}: FileUploadProps) {
  const [uploadProgress, setUploadProgress] = useState<FileUploadProgress | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<FileInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const acceptAttribute = allowedTypes.startsWith('.') 
    ? allowedTypes 
    : allowedTypes.split(',').map(t => t.trim().startsWith('.') ? t.trim() : `.${t.trim()}`).join(',');

  const validateFile = useCallback((file: File): string | null => {
    // Check file size
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return `File size must be less than ${maxSizeMB}MB`;
    }
    
    // Check file type
    const fileName = file.name.toLowerCase();
    const allowedExtensions = allowedTypes.split(',').map(ext => ext.trim().toLowerCase());
    const fileExtension = '.' + fileName.split('.').pop();
    
    if (!allowedExtensions.some(ext => fileName.endsWith(ext.replace('.', '')) || fileExtension === ext)) {
      return `File type not allowed. Supported formats: ${allowedTypes}`;
    }
    
    return null;
  }, [maxSizeMB, allowedTypes]);

  const handleFileUpload = useCallback(async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      console.error('File validation error:', validationError);
      return;
    }

    try {
      setIsLoading(true);
      
      const result = await FileAPI.uploadFileComplete(
        file,
        submissionId,
        questionId,
        accessToken,
        setUploadProgress
      );

      // Update the form value with S3 key and file metadata
      // Following PRD: store s3Key in form answer for backend processing
      const fileAnswer = {
        file: result.s3_key, // Use the S3 key from backend response
        filename: file.name,
        size: file.size,
        type: file.type,
        uploaded_at: new Date().toISOString()
      };

      onChange(fileAnswer);
      
      // Refresh file list
      await loadFiles();
      
    } catch (error) {
      console.error('File upload error:', error);
      setUploadProgress({
        file_id: '',
        filename: file.name,
        progress: 0,
        status: 'error',
        error: error instanceof Error ? error.message : 'Upload failed'
      });
    } finally {
      setIsLoading(false);
    }
  }, [questionId, submissionId, accessToken, validateFile, onChange]);

  const loadFiles = useCallback(async () => {
    try {
      const files = await FileAPI.listSubmissionFiles(
        submissionId,
        questionId,
        accessToken
      );
      setUploadedFiles(files);
    } catch (error) {
      console.error('Error loading files:', error);
    }
  }, [submissionId, questionId, accessToken]);

  const handleFileDelete = useCallback(async (fileId: string) => {
    try {
      await FileAPI.deleteFile(fileId, accessToken);
      onChange(null);
      await loadFiles();
    } catch (error) {
      console.error('Error deleting file:', error);
    }
  }, [accessToken, onChange, loadFiles]);

  const handleFileDownload = useCallback(async (fileId: string) => {
    try {
      const downloadResponse = await FileAPI.generateDownloadUrl(
        { file_id: fileId },
        accessToken
      );
      
      // Open download URL in new tab
      window.open(downloadResponse.presigned_url, '_blank');
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  }, [accessToken]);

  const handleFileSelect = useCallback((file: File) => {
    handleFileUpload(file);
  }, [handleFileUpload]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]); // Only handle first file for single file upload
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const currentFile = value && typeof value === 'object' && value.file ? value : null;
  const hasFile = !!currentFile;
  const isUploading = uploadProgress?.status === 'uploading';
  const isProcessing = uploadProgress?.status === 'processing';
  const hasError = uploadProgress?.status === 'error';

  return (
    <div className="space-y-3">
      {/* Upload Area */}
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 cursor-pointer relative",
          hasFile 
            ? "border-green-300 bg-green-50 hover:border-green-400" 
            : "border-gray-300 bg-white hover:border-blue-400",
          error || hasError ? "border-red-400 bg-red-50" : "",
          isLoading ? "pointer-events-none opacity-50" : ""
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onClick={() => {
          if (!isLoading) {
            const input = document.getElementById(`file-input-${questionId}`);
            input?.click();
          }
        }}
      >
        <AnimatePresence mode="wait">
          {isUploading || isProcessing ? (
            <motion.div
              key="uploading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-4"
            >
              <Loader2 className="mx-auto h-12 w-12 text-blue-600 animate-spin" />
              <p className="text-blue-800 font-medium">
                {isUploading ? 'Uploading...' : 'Processing...'}
              </p>
              {uploadProgress && (
                <div className="space-y-2">
                  <Progress value={uploadProgress.progress} className="w-full" />
                  <p className="text-sm text-blue-600">
                    {uploadProgress.filename} - {uploadProgress.progress.toFixed(0)}%
                  </p>
                </div>
              )}
            </motion.div>
          ) : hasFile ? (
            <motion.div
              key="uploaded"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="p-4"
            >
              <FileDisplay
                fileAnswer={currentFile}
                accessToken={accessToken}
                mode="edit"
                onDelete={() => {
                  if (currentFile.file) {
                    handleFileDelete(currentFile.file);
                  } else {
                    onChange(null);
                  }
                }}
                className="border-0 shadow-none p-0"
              />
            </motion.div>
          ) : hasError ? (
            <motion.div
              key="error"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-4"
            >
              <AlertCircle className="mx-auto h-12 w-12 text-red-600" />
              <p className="text-red-800 font-medium">Upload Failed</p>
              <p className="text-sm text-red-600">{uploadProgress?.error}</p>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setUploadProgress(null)}
                className="text-blue-600 hover:text-blue-800"
              >
                Try Again
              </Button>
            </motion.div>
          ) : (
            <motion.div
              key="empty"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-600 mb-2">Drag and drop files here, or click to browse</p>
              <p className="text-sm text-gray-500">
                Supports: {allowedTypes} (Max {maxSizeMB}MB)
              </p>
            </motion.div>
          )}
        </AnimatePresence>
        
        <Input
          id={`file-input-${questionId}`}
          type="file"
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              handleFileSelect(file);
            }
          }}
          className="hidden"
          accept={acceptAttribute}
          disabled={isLoading}
        />
      </div>
      
      {/* File validation info */}
      <div className="text-xs text-gray-500 space-y-1">
        <p>• Maximum file size: {maxSizeMB}MB</p>
        <p>• Allowed formats: {allowedTypes}</p>
      </div>
    </div>
  );
}
