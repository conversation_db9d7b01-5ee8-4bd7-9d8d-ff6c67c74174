"use client"

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { FileText } from 'lucide-react';
import { Icons } from '@/components/icons';
import { cn } from '@/lib/utils';
import { validateField } from '@/components/core/form-preview/validation-engine';
import { type Question } from '@/lib/types/form';

interface QuestionRendererProps {
  question: Question;
  value: any;
  error?: string;
  onChange: (value: any) => void;
}

export function QuestionRenderer({ question, value, error, onChange }: QuestionRendererProps) {
  const [focused, setFocused] = useState(false);
  const [calendarOpen, setCalendarOpen] = useState(false);

  const handleChange = (newValue: any) => {
    onChange(newValue);
  };

  const renderInput = () => {
    const questionType = question.type.toLowerCase();

    switch (questionType) {
      case 'short_text':
        return (
          <Input
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            placeholder={question.help_text || `Enter ${question.label.toLowerCase()}...`}
            required={question.required}
            className={cn(
              "transition-all duration-200 border-2",
              focused ? "border-blue-400 shadow-lg" : "border-gray-200",
              error ? "border-red-400" : ""
            )}
          />
        );

      case 'long_text':
        return (
          <Textarea
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            placeholder={question.help_text || `Enter ${question.label.toLowerCase()}...`}
            required={question.required}
            className={cn(
              "min-h-[120px] transition-all duration-200 border-2 resize-y",
              focused ? "border-blue-400 shadow-lg" : "border-gray-200",
              error ? "border-red-400" : ""
            )}
          />
        );

      case 'number':
        return (
          <Input
            type="number"
            value={value || ''}
            onChange={(e) => handleChange(e.target.value ? Number(e.target.value) : null)}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            placeholder="Enter number..."
            min={question.validation?.min}
            max={question.validation?.max}
            className={cn(
              "transition-all duration-200 border-2",
              focused ? "border-blue-400 shadow-lg" : "border-gray-200",
              error ? "border-red-400" : ""
            )}
          />
        );

      case 'range':
        return (
          <div className="space-y-4">
            <Slider
              value={[value || question.validation?.min || 0]}
              onValueChange={(values) => handleChange(values[0])}
              min={question.validation?.min || 0}
              max={question.validation?.max || 100}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-gray-500">
              <span>{question.validation?.min || 0}</span>
              <span className="font-medium text-blue-600">{value || question.validation?.min || 0}</span>
              <span>{question.validation?.max || 100}</span>
            </div>
          </div>
        );

      case 'single_select':
        return (
          <div className="space-y-3">
            {question.options?.map((option) => (
              <motion.div
                key={option.value}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant={value === option.value ? "default" : "outline"}
                  onClick={() => handleChange(option.value)}
                  className={cn(
                    "w-full justify-start text-left h-auto p-4 transition-all duration-200",
                    value === option.value
                      ? "bg-blue-600 text-white shadow-lg"
                      : "bg-white hover:bg-blue-50 border-2 border-gray-200 hover:border-blue-300"
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      "w-4 h-4 rounded-full border-2 flex items-center justify-center",
                      value === option.value ? "border-white" : "border-gray-400"
                    )}>
                      {value === option.value && (
                        <div className="w-2 h-2 rounded-full bg-white" />
                      )}
                    </div>
                    <span className="font-medium">{option.label}</span>
                  </div>
                </Button>
              </motion.div>
            ))}
          </div>
        );

      case 'multi_select':
        const selectedValues = Array.isArray(value) ? value : [];
        return (
          <div className="space-y-3">
            {question.options?.map((option) => {
              const isSelected = selectedValues.includes(option.value);
              return (
                <motion.div
                  key={option.value}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    variant="outline"
                    onClick={() => {
                      const newValues = isSelected
                        ? selectedValues.filter(v => v !== option.value)
                        : [...selectedValues, option.value];
                      handleChange(newValues);
                    }}
                    className={cn(
                      "w-full justify-start text-left h-auto p-4 transition-all duration-200",
                      isSelected
                        ? "bg-blue-50 border-blue-400 text-blue-900"
                        : "bg-white hover:bg-gray-50 border-2 border-gray-200 hover:border-gray-300"
                    )}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={cn(
                        "w-4 h-4 rounded border-2 flex items-center justify-center",
                        isSelected ? "border-blue-600 bg-blue-600" : "border-gray-400"
                      )}>
                        {isSelected && (
                          <Icons.check className="w-3 h-3 text-white" />
                        )}
                      </div>
                      <span className="font-medium">{option.label}</span>
                    </div>
                  </Button>
                </motion.div>
              );
            })}
          </div>
        );

      case 'boolean':
        return (
          <div className="flex items-center justify-start space-x-8 p-6 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <Switch
                checked={value === true}
                onCheckedChange={(checked) => handleChange(checked)}
                className="data-[state=checked]:bg-blue-600"
              />
              <Label className="text-lg font-medium">
                {value === true ? 'Yes' : value === false ? 'No' : 'Select'}
              </Label>
            </div>
          </div>
        );

      case 'date':
        return (
          <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal h-12 border-2 transition-all duration-200",
                  !value && "text-muted-foreground",
                  focused ? "border-blue-400 shadow-lg" : "border-gray-200",
                  error ? "border-red-400" : ""
                )}
                onFocus={() => setFocused(true)}
                onBlur={() => setFocused(false)}
              >
                <Icons.calendar className="mr-2 h-4 w-4" />
                {value ? format(new Date(value), "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={value ? new Date(value) : undefined}
                onSelect={(date) => {
                  handleChange(date?.toISOString());
                  setCalendarOpen(false);
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );

      case 'file':
        const maxSizeMB = question.validation?.max || 10;
        const allowedTypes = question.validation?.regex || '.pdf,.doc,.docx,.jpg,.jpeg,.png';
        const acceptAttribute = allowedTypes.startsWith('.') ? allowedTypes : allowedTypes.split(',').map(t => t.trim().startsWith('.') ? t.trim() : `.${t.trim()}`).join(',');
        
        const validateFile = (file: File): string | null => {
          // Check file size
          const maxSizeBytes = maxSizeMB * 1024 * 1024;
          if (file.size > maxSizeBytes) {
            return `File size must be less than ${maxSizeMB}MB`;
          }
          
          // Check file type
          const fileName = file.name.toLowerCase();
          const allowedExtensions = allowedTypes.split(',').map(ext => ext.trim().toLowerCase());
          const fileExtension = '.' + fileName.split('.').pop();
          
          if (!allowedExtensions.some(ext => fileName.endsWith(ext.replace('.', '')) || fileExtension === ext)) {
            return `File type not allowed. Supported formats: ${allowedTypes}`;
          }
          
          return null;
        };

        const handleFileSelect = (file: File) => {
          const validationError = validateFile(file);
          if (validationError) {
            // You might want to show this error in a toast or set it as field error
            console.error('File validation error:', validationError);
            return;
          }
          
          // Store file info object instead of just filename
          const fileInfo = {
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: file.lastModified,
            // In a real implementation, you'd upload the file and store the URL/ID
            // For now, we'll just store the file metadata
            id: `temp_${Date.now()}_${file.name}`,
          };
          
          handleChange(fileInfo);
        };

        const handleDrop = (e: React.DragEvent) => {
          e.preventDefault();
          e.stopPropagation();
          
          const files = Array.from(e.dataTransfer.files);
          if (files.length > 0) {
            handleFileSelect(files[0]); // Only handle first file for single file upload
          }
        };

        const handleDragOver = (e: React.DragEvent) => {
          e.preventDefault();
          e.stopPropagation();
        };

        const formatFileSize = (bytes: number): string => {
          if (bytes === 0) return '0 Bytes';
          const k = 1024;
          const sizes = ['Bytes', 'KB', 'MB', 'GB'];
          const i = Math.floor(Math.log(bytes) / Math.log(k));
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };

        const currentFile = value && typeof value === 'object' ? value : null;
        const hasFile = !!currentFile;

        return (
          <div className="space-y-3">
            <div
              className={cn(
                "border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 cursor-pointer relative",
                hasFile 
                  ? "border-green-300 bg-green-50 hover:border-green-400" 
                  : "border-gray-300 bg-white hover:border-blue-400",
                error ? "border-red-400 bg-red-50" : ""
              )}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onClick={() => {
                const input = document.getElementById(`file-input-${question._id || question.id}`);
                input?.click();
              }}
            >
              {hasFile ? (
                                 <>
                   <FileText className="mx-auto h-12 w-12 text-green-600 mb-4" />
                   <p className="text-green-800 font-medium mb-2">{currentFile.name}</p>
                   <p className="text-sm text-green-600 mb-4">
                     {formatFileSize(currentFile.size)} • Uploaded successfully
                   </p>
                   <Button
                     type="button"
                     variant="outline"
                     size="sm"
                     onClick={(e) => {
                       e.stopPropagation();
                       handleChange(null);
                     }}
                     className="text-red-600 hover:text-red-800 hover:bg-red-50"
                   >
                     <Icons.trash className="w-4 h-4 mr-2" />
                     Remove File
                   </Button>
                 </>
               ) : (
                 <>
                   <Icons.upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                   <p className="text-gray-600 mb-2">Drag and drop files here, or click to browse</p>
                   <p className="text-sm text-gray-500">
                     Supports: {allowedTypes} (Max {maxSizeMB}MB)
                   </p>
                 </>
               )}
               
               <Input
                 id={`file-input-${question._id || question.id}`}
                type="file"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleFileSelect(file);
                  }
                }}
                className="hidden"
                accept={acceptAttribute}
              />
            </div>
            
            {/* File validation info */}
            <div className="text-xs text-gray-500 space-y-1">
              <p>• Maximum file size: {maxSizeMB}MB</p>
              <p>• Allowed formats: {allowedTypes}</p>
              {question.help_text && <p>• {question.help_text}</p>}
            </div>
          </div>
        );

      default:
        return (
          <Input
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={`Enter ${question.label.toLowerCase()}...`}
            className="border-2 border-gray-200 focus:border-blue-400"
          />
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-3"
      data-error={!!error}
    >
      {/* Question Label */}
      <div className="flex items-center space-x-2">
        <Label className="text-lg font-semibold text-gray-900">
          {question.label}
        </Label>
        {question.required && (
          <Badge variant="destructive" className="text-xs">
            Required
          </Badge>
        )}
      </div>

      {/* Help Text */}
      {question.help_text && (
        <p className="text-sm text-gray-600 leading-relaxed">
          {question.help_text}
        </p>
      )}

      {/* Input Field */}
      <div className="relative">
        {renderInput()}

        {/* Error Message */}
        {error && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-sm text-red-600 mt-2 flex items-center space-x-1"
          >
            <Icons.alertCircle className="h-4 w-4" />
            <span>{error}</span>
          </motion.p>
        )}
      </div>
    </motion.div>
  );
}
