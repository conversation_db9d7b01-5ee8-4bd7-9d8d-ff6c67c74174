"use client"

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Icons } from '@/components/icons';
import { ErrorBoundary } from '@/components/error-boundary';
import { SharedFormHeader } from './shared-form-header';
import { FormSection } from './form-section';
import { PublicLoginScreen } from './public-login-screen';
import { SubmissionSuccess } from './submission-success';
import { usePublicAuth } from '@/lib/contexts/public-auth-context';
import { usePublicFormProgress } from '@/lib/hooks/use-public-form-progress';
import { PublicSubmissionAPI } from '@/lib/api/public-submission';
import { getVisibleQuestions, validateAnswer } from '@/lib/utils/form-logic';

// Safe animation wrapper to prevent chunk loading errors
const SafeMotionDiv = ({ children, ...props }: any) => {
  try {
    return <motion.div {...props}>{children}</motion.div>;
  } catch (error) {
    console.warn('Animation error, falling back to static div:', error);
    return <div>{children}</div>;
  }
};

interface SharedFormRendererProps {
  token: string;
}



function SharedFormRendererInner({ token }: SharedFormRendererProps) {
  const { isAuthenticated, user, submission, loading: authLoading } = usePublicAuth();
  const [formData, setFormData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submissionId, setSubmissionId] = useState<string | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side to prevent hydration mismatches
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Load form data
  useEffect(() => {
    if (!token) return;

    const fetchFormData = async () => {
      try {
        setLoading(true);
        const data = await PublicSubmissionAPI.getTokenDetails(token);
        setFormData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load form');
      } finally {
        setLoading(false);
      }
    };

    fetchFormData();
  }, [token]);

  const {
    answers,
    updateAnswer,
    progress,
    canSubmit,
    saving,
    lastSaved,
    saveProgress,
    submitForm,
    removeRepeatAnswers,
    canEdit,
    submissionStatus
  } = usePublicFormProgress(token, formData?.form);

  // Only validate after submit attempt
  useEffect(() => {
    if (formData?.form && hasAttemptedSubmit && isClient) {
      try {
        // Only validate visible and required questions
        const visibleQuestions = getVisibleQuestions(formData.form, answers);
        const validationErrors: Record<string, string> = {};
        
        visibleQuestions.forEach((question: any) => {
          if (question.required) {
            const error = validateAnswer(question, answers[question._id || question.id]);
            if (error) {
              validationErrors[question._id || question.id] = error;
            }
          }
        });
        
        setErrors(validationErrors);
      } catch (error) {
        console.error('Validation error:', error);
      }
    }
  }, [answers, formData?.form, hasAttemptedSubmit, isClient]);

  // Check if form is already submitted
  useEffect(() => {
    if (submission && submissionStatus === 'submitted') {
      setIsSubmitted(true);
      setSubmissionId(submission.id);
    }
  }, [submission, submissionStatus]);

  const handleSubmit = async () => {
    if (!formData?.form || !canEdit) return;

    // Mark that user has attempted to submit
    setHasAttemptedSubmit(true);

    try {
      // Validate all answers before submission
      const visibleQuestions = getVisibleQuestions(formData.form, answers);
      const validationErrors: Record<string, string> = {};
      
      visibleQuestions.forEach((question: any) => {
        if (question.required) {
          const error = validateAnswer(question, answers[question._id || question.id]);
          if (error) {
            validationErrors[question._id || question.id] = error;
          }
        }
      });

      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors);
        // Scroll to first error
        const firstErrorElement = document.querySelector('[data-error="true"]');
        firstErrorElement?.scrollIntoView({ behavior: 'smooth', block: 'center' });
        return;
      }

      const result = await submitForm();
      setSubmissionId(result.id);
      setIsSubmitted(true);

    } catch (error) {
      console.error('Submission failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to submit form');
    }
  };

  if (loading || authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <Icons.spinner className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600 font-medium">Loading form...</p>
        </motion.div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md mx-auto text-center"
        >
          <Card className="shadow-lg">
            <CardContent className="p-8">
              <Icons.alertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h1 className="text-xl font-bold text-gray-900 mb-2">Form Unavailable</h1>
              <p className="text-gray-600 mb-4">{error}</p>
              <p className="text-sm text-gray-500">
                Please contact the form owner for a new link.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    );
  }

  if (isSubmitted && submissionId) {
    return (
      <SubmissionSuccess
        organization={formData?.organization}
        submissionId={submissionId}
      />
    );
  }

  if (!formData) return null;

  // Show login screen if not authenticated
  if (!isAuthenticated) {
    return (
      <PublicLoginScreen
        token={token}
        organizationName={formData.organization?.name}
        formName={formData.form?.name}
        organizationLogo={formData.organization?.logo_url}
      />
    );
  }

  // Show read-only view if form is already submitted
  if (submissionStatus === 'submitted') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <Card className="shadow-lg">
              <CardContent className="p-8">
                <Icons.check className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-gray-900 mb-2">Form Already Submitted</h1>
                <p className="text-gray-600 mb-4">
                  You have already submitted this form. Thank you for your response!
                </p>
                <p className="text-sm text-gray-500">
                  If you need to make changes, please contact {formData.organization?.name || 'the form owner'}.
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    );
  }

  const { form, organization, branding } = formData;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Progress Bar */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200"
      >
        <div className="max-w-4xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Progress: {Math.round(progress)}%
            </span>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              {saving && (
                <>
                  <Icons.spinner className="h-3 w-3 animate-spin text-blue-500" />
                  <span>Saving...</span>
                </>
              )}
              {!saving && lastSaved && (
                <>
                  <Icons.check className="h-3 w-3 text-green-500" />
                  <span>Saved {lastSaved.toLocaleTimeString()}</span>
                </>
              )}
            </div>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </motion.div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <SharedFormHeader
          form={form}
          organization={organization}
          branding={branding}
        />

        {/* User Info Banner */}
        {user && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Alert className="bg-green-50 border-green-200">
              <Icons.user className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                <div className="flex items-center justify-between">
                  <span>Logged in as {user.email}</span>
                  <div className="flex items-center gap-2 text-xs">
                    {canEdit ? (
                      <span className="text-green-600">Can edit</span>
                    ) : (
                      <span className="text-orange-600">Read-only</span>
                    )}
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          </motion.div>
        )}

        {/* Form Sections */}
        <div className="space-y-8">
          {form.sections.map((section, index) => (
            <FormSection
              key={section._id}
              section={section}
              answers={answers}
              errors={hasAttemptedSubmit ? errors : {}}
              onAnswerChange={updateAnswer}
              onRemoveRepeatAnswers={removeRepeatAnswers}
              sectionIndex={index}
            />
          ))}
        </div>

        {/* Submit Button */}
        <SafeMotionDiv
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-12 text-center"
        >
          <Button
            onClick={handleSubmit}
            disabled={saving || !canSubmit || !canEdit}
            size="lg"
            className="px-12 py-4 text-lg font-semibold bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
          >
            {saving ? (
              <>
                <Icons.spinner className="mr-2 h-5 w-5 animate-spin" />
                Submitting...
              </>
            ) : (
              'Submit Form'
            )}
          </Button>

          {!canEdit && (
            <p className="mt-3 text-sm text-orange-600">
              This form has already been submitted and cannot be edited.
            </p>
          )}

          {canEdit && !canSubmit && (
            <p className="mt-3 text-sm text-gray-500">
              Please complete all required fields to submit ({Math.round(progress)}% complete)
            </p>
          )}
        </SafeMotionDiv>

        {/* Footer */}
        <SafeMotionDiv
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-16 text-center"
        >
          <div className="flex items-center justify-center space-x-2">
            <span className="text-xs text-gray-400 font-mono tracking-wider">
              POWERED BY
            </span>
            <img
              src="/Logo wo space.png"
              alt="TractionX"
              className="h-4 w-auto"
            />
          </div>
        </SafeMotionDiv>
      </div>


    </div>
  );
}

export function SharedFormRenderer({ token }: SharedFormRendererProps) {
  // Add additional safety checks
  if (!token || typeof token !== 'string') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Invalid Link</h1>
          <p className="text-gray-600">This sharing link is invalid or malformed.</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <SharedFormRendererInner token={token} />
    </ErrorBoundary>
  );
}
