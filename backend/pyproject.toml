[tool.poetry]
name = "x-app"
version = "0.1.0"
description = "Investing intelligence platform backend"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.109.0"
uvicorn = "^0.27.0"
motor = "^3.3.2"
redis = "^5.0.1"
pydantic = "^2.6.0"
pydantic-settings = "^2.1.0"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
structlog = "^24.1.0"
prometheus-client = "^0.19.0"
rq = "^1.15.1"
fastapi-mail = "^1.4.2"
boto3 = "^1.38.30"
qrcode = "^7.4.2"
aiohttp = "^3.9.3"
httpx = "^0.26.0"
requests = "^2.31.0"
watchfiles = "^0.21.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.0.0"
pytest-asyncio = "^0.23.5"
pytest-cov = "^4.1.0"
httpx = "^0.26.0"
black = "^24.1.0"
isort = "^5.13.0"
mypy = "^1.8.0"
pre-commit = "^3.6.0"
faker = "^22.6.0"
mongomock = "^4.3.0"
watchdog = "^4.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ['py311']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_optional = true