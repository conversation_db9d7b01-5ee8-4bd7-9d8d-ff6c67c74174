from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import <PERSON>GI<PERSON>pp
import re
from bson import ObjectId

from app.core import logging
from app.core.config import settings
from app.core.errors import HTTPException
from app.core.org_context import OrgContext

logger = logging.get_logger(__name__)


class OrgContextMiddleware(BaseHTTPMiddleware):
    """Middleware to handle organization context automatically."""

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        # Compile regex pattern for organization subdomains
        self.org_subdomain_pattern = re.compile(
            r'^([^.]+)\.' + re.escape(settings.DOMAIN) + r'$'
        )
        # List of paths that don't require organization context
        self.public_paths = settings.PUBLIC_PATHS

    async def dispatch(self, request: Request, call_next: callable) -> Response:
        # Always allow OPTIONS requests for CORS preflight
        if request.method == "OPTIONS":
            logger.info(f"OrgContextMiddleware: Skipping org context for OPTIONS preflight request: {request.url.path}")
            # Create a response with proper CORS headers
            response = await call_next(request)
            # Log the response headers for debugging
            headers_dict = dict(response.headers.items())
            logger.info(f"OrgContextMiddleware: OPTIONS response headers: {headers_dict}")
            return response

        # Skip for public paths
        if (request.url.path in self.public_paths or
            request.url.path.startswith("/share/") or
            request.url.path.startswith("/s/") or
            request.url.path.startswith("/embed/") or
            request.url.path.startswith("/qr/") or
            request.url.path.startswith("/public")):
            logger.info(
                f"OrgContextMiddleware: Skipping org context for public path: {request.url.path}")
            return await call_next(request)

        # Skip for non-API routes
        if not request.url.path.startswith("/api/"):
            return await call_next(request)

        # TEST_MODE: skip org context checks, set dummy state
        if settings.TEST_MODE:
            logger.info("Test mode enabled, skipping organization context")
            request.state.org_id = "000000000000000000000000"
            request.state.is_cross_org = False
            request.state.pass_through = True
            response = await call_next(request)
            response.headers["X-Org-ID"] = request.state.org_id
            response.headers["X-Is-Cross-Org"] = "false"
            return response

        try:
            user = getattr(request.state, "user", None)
            if not user:
                logger.warning(
                    "No user in request.state; skipping org context.")
                return await call_next(request)

            # Use OrgContext to extract and validate org_id from X-ORG-ID header
            org_id, is_cross_org = OrgContext.get_org_id(request, user)

            # Convert ObjectId to string if needed
            org_id_str = str(org_id) if isinstance(org_id, ObjectId) else org_id

            request.state.org_id = org_id  # Keep original ObjectId in state
            request.state.is_cross_org = is_cross_org
            request.state.pass_through = False  # Set as needed

            # Add X-ORG-ID header if not present (for consistency in downstream processing)
            if "X-ORG-ID" not in request.headers and org_id_str:
                request.headers["X-ORG-ID"] = org_id_str

            response = await call_next(request)

            # Ensure headers are strings
            response.headers["X-Org-ID"] = org_id_str
            response.headers["X-Is-Cross-Org"] = str(is_cross_org).lower()
            return response

        except HTTPException as e:
            logger.error(f"Org context error: {e.detail}")
            raise  # Let FastAPI handle and return proper error response
        except Exception as e:
            logger.error(f"Unexpected org context middleware error: {str(e)}")
            return await call_next(request)
