from typing import Callable, Optional
import logging

from fastapi import Request, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from starlette.middleware.base import BaseHTTPMiddleware
from bson import ObjectId

from app.models.audit import AuditLog
from app.core.errors import (
    InternalError,
    handle_error
)
from app.core.config import settings
from app.models.user import User
from app.core.auth_exceptions import (
    AuthError,
    TokenExpiredError,
    InvalidTokenError,
    TokenTypeError,
    MissingTokenError,
    UserNotFoundError
)


logger = logging.getLogger(__name__)


class AuthMiddleware(BaseHTTPMiddleware):
    def __init__(
        self,
        app,
        required_permissions: Optional[dict] = None
    ):
        super().__init__(app)
        self.required_permissions = required_permissions or {}
        self.security = HTTPBearer()
        self.public_paths = settings.PUBLIC_PATHS

    async def dispatch(self, request: Request, call_next: Callable):
        logger.info(f"Processing request: {request.method} {request.url.path}")

        # Always allow OPTIONS requests for CORS preflight
        if request.method == "OPTIONS":
            logger.info(f"Skipping auth for OPTIONS preflight request: {request.url.path}")
            # Create a response with proper CORS headers
            response = await call_next(request)
            # Log the response headers for debugging
            headers_dict = dict(response.headers.items())
            logger.info(f"OPTIONS response headers: {headers_dict}")
            return response

        # Skip auth for public routes, superuser creation, and login
        if request.url.path in self.public_paths:
            logger.info(f"Skipping auth for public route: {request.url.path}")
            return await call_next(request)

        # Skip auth for sharing endpoints (pattern matching)
        if (request.url.path.startswith("/api/v1/forms/share/") or
            request.url.path.startswith("/api/v1/magic-link") or
            request.url.path.startswith("/api/v1/public")):
            logger.info(f"Skipping auth for sharing/magic-link route: {request.url.path}")
            return await call_next(request)

        # If in test mode, skip all auth checks
        if settings.TEST_MODE:
            logger.info("Test mode enabled, skipping authentication")
            # Create a dummy user for testing
            request.state.user = {
                "id": ObjectId("000000000000000000000000"),
                "email": "<EMAIL>",
                "name": "Test User",
                "is_superuser": True,
                "org_ids": ["000000000000000000000000"],
                "org_id": "000000000000000000000000",
                "role_id": "000000000000000000000000",
                "status": "active"
            }
            return await call_next(request)

        try:
            # Get services from request state
            auth_service = request.app.state.auth_service

            # Extract token
            try:
                auth = await self.security(request)
                if not auth:
                    raise MissingTokenError()

                # Verify token
                logger.info(f"Verifying token: {auth.credentials}")
                token = await auth_service.verify_token(auth.credentials)
                if not token:
                    raise InvalidTokenError()

                logger.info(f"User authenticated: {token.sub}")

                # Get user
                user = await User.find_one({"_id": token.sub})
                if not user:
                    raise UserNotFoundError(user_id=str(token.sub))

            except (TokenExpiredError, InvalidTokenError, TokenTypeError, MissingTokenError, UserNotFoundError) as auth_error:
                # Just re-raise our custom exceptions - they'll be handled by the global exception handler
                logger.warning(f"Auth error in middleware: {auth_error.detail}")
                raise auth_error

            # moved the code to work at a route level
            # Check if user is superuser
            # is_superuser = user.is_superuser
            # if is_superuser:
            #     logger.info(
            #         f"User {user.id} is superuser, bypassing permission checks")
            # else:
            #     # Check permissions if required and user is not superuser
            #     if self.required_permissions:
            #         resource = self.required_permissions.get("resource")
            #         action = self.required_permissions.get("action")
            #         logger.info(
            #             f"Checking permissions for resource: {resource}, action: {action}")

            #         if resource and action:
            #             has_permission = await rbac_service.check_permission(
            #                 str(user.id),
            #                 resource,
            #                 action
            #             )
            #             logger.info(
            #                 f"Permission check result: {has_permission}")
            #             if not has_permission:
            #                 error = AuthorizationError(
            #                     message="Insufficient permissions",
            #                     details={
            #                         "user_id": str(user.id),
            #                         "resource": resource,
            #                         "action": action
            #                     }
            #                 )
            #                 await handle_error(error)
            #                 raise error.to_http_exception()

            # Log access
            audit_log = AuditLog(
                user_id=ObjectId(str(user.id)),
                action="access",
                entity_type="api",
                entity_id=ObjectId(str(user.id)),
                ip_address=request.client.host,
                user_agent=request.headers.get("user-agent")
            )
            await request.app.state.db.audit_logs.insert_one(audit_log.model_dump(by_alias=True))
            logger.info(f"Access logged for user {user.id}")

            # Add user to request state
            request.state.user = user
            logger.info("Request processing completed successfully")

            return await call_next(request)

        except (HTTPException, AuthError) as e:
            # Pass through HTTP exceptions and our custom auth exceptions
            logger.warning(f"Auth/HTTP Exception: {str(e)}")
            raise e
        except Exception as e:
            # Check if it's a token-related exception
            error_message = str(e)
            if "token" in error_message.lower() and "expire" in error_message.lower():
                logger.warning(f"Token expired exception: {error_message}")
                from app.core.auth_exceptions import TokenExpiredError
                raise TokenExpiredError()

            # For other exceptions, create an internal error
            error = InternalError(
                message="Unexpected error during request processing",
                details={"error": str(e)},
                should_alert=True
            )
            await handle_error(error)
            raise error.to_http_exception()
