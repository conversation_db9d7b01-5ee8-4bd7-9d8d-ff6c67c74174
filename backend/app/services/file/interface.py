"""
File service interface for TractionX submission file handling.

This module defines the interface for file operations including S3 storage,
presigned URL generation, and file metadata management.
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from app.models.file import SubmissionFile, FileAccessLog


class FileServiceInterface(ABC):
    """Interface for file services."""
    
    @abstractmethod
    async def generate_presigned_upload_url(
        self,
        submission_id: str,
        question_id: str,
        original_filename: str,
        mime_type: str,
        file_size: int,
        uploaded_by_user_id: str,
        uploaded_by_email: str,
        org_id: str,
        access_type: str = "public_user"
    ) -> Dict[str, Any]:
        """
        Generate a presigned URL for file upload.
        
        Args:
            submission_id: ID of the submission
            question_id: ID of the question
            original_filename: Original filename
            mime_type: MIME type of the file
            file_size: Size of the file in bytes
            uploaded_by_user_id: ID of the user uploading
            uploaded_by_email: Email of the user uploading
            org_id: Organization ID
            access_type: Type of access (public_user, organization_user)
        
        Returns:
            Dict containing presigned_url, s3_key, file_id, and expiry info
        """
        pass
    
    @abstractmethod
    async def confirm_file_upload(
        self,
        file_id: str,
        checksum: Optional[str] = None
    ) -> Optional[SubmissionFile]:
        """
        Confirm that a file has been successfully uploaded to S3.
        
        Args:
            file_id: ID of the file record
            checksum: Optional file checksum for verification
        
        Returns:
            Updated SubmissionFile object or None if not found
        """
        pass
    
    @abstractmethod
    async def generate_presigned_download_url(
        self,
        file_id: str,
        accessed_by_email: str,
        access_type: str,
        submission_status: str = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate a presigned URL for file download.
        
        Args:
            file_id: ID of the file
            accessed_by_email: Email of the user requesting access
            access_type: Type of access (public_user, organization_user, investor)
            submission_status: Status of the submission (for investor access)
            ip_address: IP address of the request
            user_agent: User agent string
        
        Returns:
            Dict containing presigned_url and expiry info
        """
        pass
    
    @abstractmethod
    async def delete_file(
        self,
        file_id: str,
        deleted_by_email: str,
        access_type: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        Delete a file (soft delete).
        
        Args:
            file_id: ID of the file to delete
            deleted_by_email: Email of the user deleting the file
            access_type: Type of access
            ip_address: IP address of the request
            user_agent: User agent string
        
        Returns:
            True if deletion was successful
        """
        pass
    
    @abstractmethod
    async def list_submission_files(
        self,
        submission_id: str,
        question_id: Optional[str] = None,
        accessed_by_email: str = None,
        access_type: str = None
    ) -> List[Dict[str, Any]]:
        """
        List files for a submission.
        
        Args:
            submission_id: ID of the submission
            question_id: Optional question ID to filter by
            accessed_by_email: Email of the user requesting the list
            access_type: Type of access
        
        Returns:
            List of file metadata dictionaries
        """
        pass
    
    @abstractmethod
    async def get_file_by_id(
        self,
        file_id: str,
        accessed_by_email: str = None,
        access_type: str = None
    ) -> Optional[SubmissionFile]:
        """
        Get file by ID with access control.
        
        Args:
            file_id: ID of the file
            accessed_by_email: Email of the user requesting the file
            access_type: Type of access
        
        Returns:
            SubmissionFile object or None if not found/no access
        """
        pass
    
    @abstractmethod
    async def validate_file_upload(
        self,
        filename: str,
        mime_type: str,
        file_size: int,
        question_validation: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Validate file upload against system and question-specific rules.
        
        Args:
            filename: Original filename
            mime_type: MIME type
            file_size: File size in bytes
            question_validation: Question-specific validation rules
        
        Returns:
            Dict with 'valid' boolean and 'errors' list
        """
        pass
    
    @abstractmethod
    async def log_file_access(
        self,
        file_id: str,
        submission_id: str,
        org_id: str,
        accessed_by_email: str,
        access_type: str,
        action: str,
        success: bool,
        error_message: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> FileAccessLog:
        """
        Log file access for audit purposes.
        
        Args:
            file_id: ID of the file
            submission_id: ID of the submission
            org_id: Organization ID
            accessed_by_email: Email of the user
            access_type: Type of access
            action: Action performed (download, view, delete)
            success: Whether the action was successful
            error_message: Error message if action failed
            ip_address: IP address of the request
            user_agent: User agent string
        
        Returns:
            Created FileAccessLog object
        """
        pass
    
    @abstractmethod
    async def cleanup_orphaned_files(self, max_age_hours: int = 24) -> int:
        """
        Clean up orphaned files that were never confirmed as uploaded.
        
        Args:
            max_age_hours: Maximum age in hours for orphaned files
        
        Returns:
            Number of files cleaned up
        """
        pass
