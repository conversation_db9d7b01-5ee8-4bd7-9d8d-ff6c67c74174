from typing import Optional, Dict, Any
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig
from pydantic import EmailStr
import httpx

from app.core.config import settings
from app.services.email.interface import IEmailService
from app.core.logging import get_logger

logger = get_logger(__name__)


class EmailService(IEmailService):
    def __init__(self):
        self.use_resend = settings.USE_RESEND and settings.RESEND_API_KEY

        if not self.use_resend:
            # Initialize FastMail for SMTP
            self.config = ConnectionConfig(
                MAIL_USERNAME=settings.SMTP_USERNAME,
                MAIL_PASSWORD=settings.SMTP_PASSWORD,
                MAIL_FROM=settings.SMTP_FROM,
                MAIL_PORT=settings.SMTP_PORT,
                MAIL_SERVER=settings.SMTP_HOST,
                MAIL_FROM_NAME=settings.SMTP_FROM_NAME,
                MAIL_STARTTLS=True,
                MAIL_SSL_TLS=False,
                USE_CREDENTIALS=True,
                VALIDATE_CERTS=True,
            )
            self.fastmail = FastMail(self.config)
        else:
            self.fastmail = None
            logger.info("Using Resend for email delivery")

    async def _send_via_resend(self, to: str, subject: str, html_content: str) -> None:
        """Send email via Resend API."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.resend.com/emails",
                    headers={
                        "Authorization": f"Bearer {settings.RESEND_API_KEY}",
                        "Content-Type": "application/json",
                    },
                    json={
                        "from": f"TractionX <outreach@{settings.RESEND_DOMAIN}>",
                        "to": [to],
                        "subject": subject,
                        "html": html_content,
                    },
                )

                if response.status_code != 200:
                    logger.error(f"Resend API error: {response.status_code} - {response.text}")
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Failed to send email via Resend",
                    )

                logger.info(f"Email sent via Resend to {to}")

        except httpx.RequestError as e:
            logger.error(f"Resend API request error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send email via Resend",
            )

    async def _send_email(self, to: str, subject: str, html_content: str) -> None:
        """Send email using configured service (Resend or SMTP)."""
        if self.use_resend:
            await self._send_via_resend(to, subject, html_content)
        else:
            # Use FastMail/SMTP
            message = MessageSchema(
                subject=subject, recipients=[to], body=html_content, subtype="html"
            )
            await self.fastmail.send_message(message)
            logger.info(f"Email sent via SMTP to {to}")

    async def send_magic_link_email(
        self,
        email: str,
        magic_link_url: str,
        investor_name: Optional[str] = None,
        form_name: Optional[str] = None,
    ) -> None:
        """Send magic link email for shared form access."""
        try:
            subject = f"Access Your Form{f' from {investor_name}' if investor_name else ''}"

            html_content = f"""
            <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h1 style="color: #2563eb;">Access Your Form</h1>
                        {f'<p>You have been invited by <strong>{investor_name}</strong> to fill out a form.</p>' if investor_name else ''}
                        {f'<p><strong>Form:</strong> {form_name}</p>' if form_name else ''}
                        <p>Click the link below to access the form:</p>
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{magic_link_url}"
                               style="background-color: #2563eb; color: white; padding: 12px 24px;
                                      text-decoration: none; border-radius: 6px; display: inline-block;">
                                Access Form
                            </a>
                        </div>
                        <p style="color: #666; font-size: 14px;">
                            This link will expire in 15 minutes for security reasons.
                        </p>
                        <p style="color: #666; font-size: 14px;">
                            If you didn't request this, please ignore this email.
                        </p>
                        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
                        <p style="color: #999; font-size: 12px; text-align: center;">
                            Powered by TractionX
                        </p>
                    </div>
                </body>
            </html>
            """

            await self._send_email(email, subject, html_content)
            logger.info(f"Magic link email sent to {email}")

        except Exception as e:
            logger.error(f"Failed to send magic link email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send magic link email",
            )

    async def send_password_reset_email(self, email: str, token: str) -> None:
        """Send password reset email."""
        try:
            reset_url = f"{settings.FRONTEND_URL}/reset-password?token={token}"
            # message = MessageSchema(
            #     subject="Reset Your Password",
            #     recipients=[email],
            #     body=f"""
            #     <html>
            #         <body>
            #             <h1>Reset Your Password</h1>
            #             <p>Click the link below to reset your password:</p>
            #             <p><a href="{reset_url}">Reset Password</a></p>
            #             <p>If you didn't request this, please ignore this email.</p>
            #             <p>This link will expire in {settings.PASSWORD_RESET_TOKEN_EXPIRY // 60} minutes.</p>
            #         </body>
            #     </html>
            #     """,
            #     subtype="html"
            # )
            # await self.fastmail.send_message(message)
            logger.info(f"Password reset email sent to {email}")
            return {"message": f"Password reset email sent to {email}", "reset_url": reset_url}
        except Exception as e:
            logger.error(f"Failed to send password reset email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send password reset email",
            )

    async def send_invitation_email(self, email: str, token: str) -> None:
        """Send user invitation email."""
        try:
            invite_url = f"{settings.FRONTEND_URL}/accept-invitation?token={token}"
            message = MessageSchema(
                subject="You've Been Invited",
                recipients=[email],
                body=f"""
                <html>
                    <body>
                        <h1>Welcome to TractionX</h1>
                        <p>You've been invited to join TractionX. Click the link below to accept the invitation:</p>
                        <p><a href="{invite_url}">Accept Invitation</a></p>
                        <p>This link will expire in {settings.INVITATION_TOKEN_EXPIRY // 3600} hours.</p>
                    </body>
                </html>
                """,
                subtype="html",
            )
            # TODO: Uncomment this when we have a real email
            # await self.fastmail.send_message(message)
            logger.info(f"Invitation email sent to {message}")
        except Exception as e:
            logger.error(f"Failed to send invitation email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send invitation email",
            )

    async def send_welcome_email(self, email: str, name: Optional[str] = None) -> None:
        """Send welcome email to new user."""
        try:
            message = MessageSchema(
                subject="Welcome to TractionX",
                recipients=[email],
                body=f"""
                <html>
                    <body>
                        <h1>Welcome to TractionX</h1>
                        <p>Hi {name or 'there'},</p>
                        <p>Welcome to TractionX! We're excited to have you on board.</p>
                        <p>Get started by exploring our platform and features.</p>
                    </body>
                </html>
                """,
                subtype="html",
            )
            await self.fastmail.send_message(message)
            logger.info(f"Welcome email sent to {email}")
        except Exception as e:
            logger.error(f"Failed to send welcome email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send welcome email",
            )

    async def send_organization_invitation(self, email: str, org_name: str, token: str) -> None:
        """Send organization invitation email."""
        try:
            invite_url = f"{settings.FRONTEND_URL}/accept-org-invitation?token={token}"
            message = MessageSchema(
                subject=f"Invitation to Join {org_name}",
                recipients=[email],
                body=f"""
                <html>
                    <body>
                        <h1>Join {org_name} on TractionX</h1>
                        <p>You've been invited to join {org_name} on TractionX. Click the link below to accept the invitation:</p>
                        <p><a href="{invite_url}">Accept Invitation</a></p>
                        <p>This link will expire in {settings.INVITATION_TOKEN_EXPIRY // 3600} hours.</p>
                    </body>
                </html>
                """,
                subtype="html",
            )
            await self.fastmail.send_message(message)
            logger.info(f"Organization invitation email sent to {email}")
        except Exception as e:
            logger.error(f"Failed to send organization invitation email to {email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send organization invitation email",
            )


email_service = EmailService()
