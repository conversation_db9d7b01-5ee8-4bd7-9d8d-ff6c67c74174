from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from fastapi import Depends, HTTPException, status, Query, Request
from pydantic import BaseModel, <PERSON>, model_validator
from bson import ObjectId

from motor.motor_asyncio import AsyncIOMotorDatabase
from app.models.answer import AnswerSchema
from app.models.deal import Deal

from app.models.base import TractionXModel
from app.models.form import Form, Section, Question, Submission, SectionWithQuestions, FormWithDetails
from app.services.form.interfaces import FormServiceInterface
from app.services.factory import get_form_service, get_queue_service, get_job_service, get_sharing_service
from app.services.sharing.interfaces import SharingServiceInterface
from app.utils.jobs.job_utils import create_job_chain
from app.core.errors import DatabaseError
from app.core.database import get_database
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.api.base import BaseAPIRouter
from app.models.user import User
from app.models.audit import AuditLog
from app.utils.common import ObjectIdField
from app.utils.model.helper import partial_model
from app.utils.rbac.rbac import rbac_register
from app.core.logging import get_logger
from app.models.sharing import SharingConfig, SharingLink
from app.services.sharing.interfaces import SharingType

logger = get_logger(__name__)
router = BaseAPIRouter(prefix="/forms", tags=["forms"])
public_router = BaseAPIRouter(prefix="/forms", tags=["forms"], disable_auth=True, require_org=False)

# Request Models

@partial_model(suffix="Update")
class FormCreate(Form):
    pass

@partial_model()
class FormUpdate(Form):
    pass

@partial_model()
class SectionCreate(Section):
    pass

@partial_model()
class SectionUpdate(Section):
    pass

@partial_model()
class QuestionCreate(Question):
    pass

@partial_model()
class QuestionUpdate(Question):
    pass


class SubmitFormRequest(BaseModel):
    """
    Request model for submitting a form.

    Supports two formats for answers:
    1. Flat structure (backward compatible): question_id -> answer
    2. Structured format with repeatable sections

    The API will handle both formats appropriately.
    """
    # For backward compatibility, accept a flat dictionary of answers
    answers: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Flat dictionary of answers (question_id -> answer)"
    )

    # For structured submissions with repeatable sections
    answer_schema: Optional[AnswerSchema] = Field(
        default=None,
        description="Structured answer schema with repeatable sections"
    )

    # Optional metadata
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Optional metadata for the submission"
    )

    @model_validator(mode='after')
    def validate_answers_provided(self) -> 'SubmitFormRequest':
        """Ensure that either answers or answer_schema is provided."""
        if self.answers is None and self.answer_schema is None:
            raise ValueError("Either answers or answer_schema must be provided")
        return self


class UpdateSubmissionRequest(BaseModel):
    """
    Request model for updating an existing submission.

    Supports the same answer formats as SubmitFormRequest but all fields are optional.
    This allows for partial updates (e.g., autosave, status updates, metadata updates).
    """
    # Submission identifier (can be provided in body or as path/query param)
    submission_id: Optional[str] = Field(
        default=None,
        description="ID of the submission to update (optional if provided in path)"
    )

    # For backward compatibility, accept a flat dictionary of answers
    answers: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Flat dictionary of answers (question_id -> answer)"
    )

    # For structured submissions with repeatable sections
    answer_schema: Optional[AnswerSchema] = Field(
        default=None,
        description="Structured answer schema with repeatable sections"
    )

    # Optional metadata
    metadata: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Optional metadata for the submission"
    )

    # Optional status update
    status: Optional[str] = Field(
        default=None,
        description="Optional status update (e.g., 'draft', 'submitted', 'in_review')"
    )


class VisibleQuestionsRequest(BaseModel):
    answers: Dict[str, Any] = Field(default_factory=dict, description="Current answers to determine visibility")


class SequentialFormRequest(BaseModel):
    answers: Dict[str, Any] = Field(default_factory=dict, description="Current answers to determine visibility and ordering")


class ComprehensiveFormUpdateRequest(TractionXModel):
    """
    Request model for comprehensive form updates.

    This model is used for both POST and PUT requests to the comprehensive endpoint.
    It handles all form data including metadata, sections, and questions.
    """
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, min_length=1, max_length=500)
    is_active: Optional[bool] = None
    status: Optional[str] = None
    sections: List[SectionUpdate] = Field(default_factory=list)
    default_section_ids: Optional[List[str]] = None

    @model_validator(mode='after')
    def clean_null_values(self) -> 'ComprehensiveFormUpdateRequest':
        """Remove None values from nested objects to prevent overwriting with nulls."""
        # Clean sections
        for section in self.sections:
            # Remove None values from section attributes
            for key, value in list(section.__dict__.items()):
                if value is None and key not in ['id', '_id']:
                    delattr(section, key)

            # Clean questions in each section
            if hasattr(section, 'questions'):
                for question in section.questions:
                    # Remove None values from question attributes
                    for key, value in list(question.__dict__.items()):
                        if value is None and key not in ['id', '_id']:
                            delattr(question, key)

        return self


class ReorderItem(BaseModel):
    id: str = Field(..., description="ID of the item to reorder")
    order: int = Field(..., ge=0, description="New order position")

class ReorderRequest(BaseModel):
    items: List[ReorderItem] = Field(..., min_items=1, description="List of items to reorder")


# Response Models

# class FormWithDetails(BaseModel):
#     id: ObjectIdField = Field(..., alias="_id")
#     name: str
#     description: str
#     version: int
#     is_active: bool
#     sections: List[SectionWithQuestions]
#     default_section_ids: List[ObjectIdField]
#     created_at: datetime
#     updated_at: datetime

#     model_config = ConfigDict(
#         arbitrary_types_allowed=True,
#         populate_by_name=True,
#         json_encoders={ObjectIdField: str}
#     )

# API Routes


@router.post("")
@rbac_register(resource="form", action="create", group="Forms", description="Create new form")
async def create_form(
    CreateFormRequest: FormCreate,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    db: AsyncIOMotorDatabase = Depends(get_database),
    form_service: FormServiceInterface = Depends(get_form_service)
):
    """Create a new form with organization context."""
    org_id, is_cross_org = org_context

    # Create form with org_id
    form = await form_service.create_form(
        name=CreateFormRequest.name,
        description=CreateFormRequest.description,
        org_id=org_id,
        user_id=current_user.id
    )
    logger.info(f"Created form: {form}")

    # Log form creation with org context
    audit_log = AuditLog(
        user_id=current_user.id,
        org_id=org_id,  # Add org context to audit
        action="create_form",
        entity_type="form",
        entity_id=form['_id'],
        metadata={"name": CreateFormRequest.name}
    )
    await db.audit_logs.insert_one(audit_log.dict(by_alias=True))

    return form


@router.get("")
@rbac_register(resource="form", action="view", group="Forms", description="View forms")
async def list_forms(
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
    form_service: FormServiceInterface = Depends(get_form_service)
):
    """List all forms in the organization."""
    forms = await form_service.list_forms(current_user.org_id)
    logger.info(f"Found {len(forms) if forms else 0} forms for org {current_user.org_id}")
    return forms


@router.get("/{form_id}")
@rbac_register(resource="form", action="view", group="Forms", description="View form")
async def get_form(
    form_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
    form_service: FormServiceInterface = Depends(get_form_service)
):
    """Get a specific form."""

    # Get form
    form = await form_service.get_form(form_id)
    if not form:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Form not found"
        )

    return form


@router.put("/{form_id}")
@rbac_register(resource="form", action="edit", group="Forms", description="Edit form")
async def update_form(
    form_id: str,
    form_update: FormUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
    form_service: FormServiceInterface = Depends(get_form_service)
):
    """
    Update an existing form with versioning support.

    This endpoint creates a snapshot of the current form state before applying updates,
    allowing for version history tracking and rollback capabilities.
    """
    # Check permissions
    update_data = form_update.model_dump(exclude_unset=True)
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No update data provided"
        )

    # Update form with versioning support
    form = await form_service.update_form(
        form_id=form_id,
        update=update_data,
        actor_id=str(current_user.id)
    )

    if not form:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Form not found"
        )

    # Log form update
    audit_log = AuditLog(
        user_id=current_user.id,
        action="update_form",
        entity_type="form",
        entity_id=ObjectId(form_id),
        metadata={
            "update_data": update_data,
            "version": form.get("version")
        }
    )
    await db.audit_logs.insert_one(audit_log.model_dump(by_alias=True))

    return form


@router.delete("/{form_id}")
@rbac_register(resource="form", action="delete", group="Forms", description="Delete form")
async def delete_form(
    form_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
    form_service: FormServiceInterface = Depends(get_form_service)
):
    """
    Delete a form.

    This endpoint archives the form instead of permanently deleting it,
    creating a snapshot of the current form state before marking it as deleted.
    """
    # Archive the form instead of deleting it
    # success = await form_service.archive_form(form_id, str(current_user.id))
    # if not success:
    #     raise HTTPException(
    #         status_code=status.HTTP_404_NOT_FOUND,
    #         detail="Form not found"
    #     )
    success = await form_service.delete_form(form_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Form not found"
        )

    # Log form deletion
    audit_log = AuditLog(
        user_id=current_user.id,
        action="delete_form",
        entity_type="form",
        entity_id=ObjectId(form_id)
    )
    await db.audit_logs.insert_one(audit_log.model_dump(by_alias=True))

    return {"message": "Form deleted successfully"}


@router.get("/{form_id}/details", response_model=FormWithDetails)
async def get_form_details(
    form_id: str,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> FormWithDetails:
    """Get form with all sections and questions."""
    form_details = await form_service.get_form_with_details(form_id)
    if not form_details:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Form not found"
        )
    return form_details.model_dump(by_alias=True)


@router.post("/{form_id}/version", response_model=Form)
@rbac_register(resource="form", action="create", group="Forms", description="Create new form version")
async def create_form_version(
    form_id: str,
    current_user: User = Depends(get_current_user),
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Form:
    """Create a new version of an existing form."""
    new_form = await form_service.create_form_version(form_id, str(current_user.id))
    if not new_form:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Form not found"
        )
    return new_form


@router.get("/{form_id}/versions")
@rbac_register(resource="form", action="view", group="Forms", description="Get form version history")
async def get_form_version_history(
    form_id: str,
    limit: int = Query(10, ge=1, le=100),
    form_service: FormServiceInterface = Depends(get_form_service)
):
    """Get version history for a form."""
    versions = await form_service.get_form_version_history(form_id, limit)
    return versions


@router.post("/{form_id}/rollback/{version}")
@rbac_register(resource="form", action="edit", group="Forms", description="Roll back form to a previous version")
async def rollback_form_to_version(
    form_id: str,
    version: int,
    current_user: User = Depends(get_current_user),
    form_service: FormServiceInterface = Depends(get_form_service)
):
    """Roll back a form to a specific version."""
    rolled_back_form = await form_service.rollback_form_to_version(form_id, version, str(current_user.id))
    if not rolled_back_form:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Form version {version} not found"
        )
    return rolled_back_form


@router.api_route("/{form_id}/comprehensive", methods=["POST", "PUT"], response_model=Form)
@rbac_register(resource="form", action="edit", group="Forms", description="Update form with all sections and questions")
async def update_form_comprehensive(
    form_id: str,
    request: ComprehensiveFormUpdateRequest,
    form_service: FormServiceInterface = Depends(get_form_service),
    current_user: User = Depends(get_current_user)
) -> Form:
    """
    Update form with all its sections and questions in a single request.

    This endpoint supports both POST and PUT methods for comprehensive form updates.
    It handles updating form metadata, sections, and questions in a single operation.
    """
    try:
        logger.info(f"Processing comprehensive update for form {form_id}")

        # Get the existing form to check if it exists and for is_update flag
        existing_form = await Form.find_one({"_id": ObjectId(form_id)})
        if not existing_form:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Form not found"
            )

        # First update form metadata if provided
        form_update = {
            k: v for k, v in request.model_dump(exclude={"sections", "default_section_ids"}).items()
            if v is not None
        }

        if form_update:
            logger.info(f"Updating form metadata: {form_update}")
            # Update form using the base class save method with is_update=True
            for key, value in form_update.items():
                setattr(existing_form, key, value)

            # Save with is_update=True to ensure proper handling
            await existing_form.save(
                actor_id=current_user.id,
                is_update=True
            )
            logger.info("Form metadata updated successfully")

        # Update default section IDs if provided
        if request.default_section_ids is not None:
            logger.info(f"Updating default section IDs: {request.default_section_ids}")
            existing_form.default_section_ids = [
                ObjectIdField(sid) for sid in request.default_section_ids
            ]
            await existing_form.save(
                actor_id=current_user.id,
                is_update=True
            )
            logger.info("Default section IDs updated successfully")

        # Process sections
        for section_update in request.sections:
            section_id = section_update.id or getattr(section_update, '_id', None)

            if section_id:
                # Update existing section
                logger.info(f"Updating existing section: {section_id}")
                existing_section = await Section.find_one({"_id": ObjectId(section_id)})

                if not existing_section:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Section {section_id} not found"
                    )

                # Update section attributes
                update_data = section_update.model_dump(exclude={"id", "_id", "questions"})
                for key, value in update_data.items():
                    if value is not None:  # Skip None values
                        setattr(existing_section, key, value)

                # Save section with is_update=True
                await existing_section.save(
                    actor_id=current_user.id,
                    is_update=True
                )
                section = existing_section
                logger.info(f"Section {section_id} updated successfully")
            else:
                # Create new section
                logger.info(f"Creating new section for form {form_id}")
                section_data = section_update.model_dump(exclude={"id", "_id", "questions"})
                section_data["form_id"] = ObjectId(form_id)
                section_data["questions"] = []  # Will add questions after creation

                # Create new section
                new_section = Section(**section_data)
                await new_section.save(actor_id=current_user.id)

                # Add section to form's sections list
                existing_form.sections.append(new_section.id)
                await existing_form.save(
                    actor_id=current_user.id,
                    is_update=True
                )

                section = new_section
                logger.info(f"New section created with ID: {section.id}")

            # Process questions for this section
            if hasattr(section_update, 'questions') and section_update.questions:
                for question_update in section_update.questions:
                    question_id = question_update.id or getattr(question_update, '_id', None)

                    if question_id:
                        # Update existing question
                        logger.info(f"Updating existing question: {question_id}")
                        existing_question = await Question.find_one({"_id": ObjectId(question_id)})

                        if not existing_question:
                            raise HTTPException(
                                status_code=status.HTTP_404_NOT_FOUND,
                                detail=f"Question {question_id} not found"
                            )

                        # Update question attributes
                        update_data = question_update.model_dump(exclude={"id", "_id"})
                        for key, value in update_data.items():
                            if value is not None:  # Skip None values
                                setattr(existing_question, key, value)

                        # Save question with is_update=True
                        await existing_question.save(
                            actor_id=current_user.id,
                            is_update=True
                        )
                        logger.info(f"Question {question_id} updated successfully")
                    else:
                        # Create new question
                        logger.info(f"Creating new question for section {section.id}")
                        question_data = question_update.model_dump(exclude={"id", "_id"})
                        question_data["section_id"] = section.id

                        # Create new question
                        new_question = Question(**question_data)
                        await new_question.save(actor_id=current_user.id)

                        # Add question to section's questions list
                        if section.questions is None:
                            section.questions = []
                        section.questions.append(new_question.id)
                        await section.save(
                            actor_id=current_user.id,
                            is_update=True
                        )
                        logger.info(f"New question created with ID: {new_question.id}")

        # Return updated form with all details
        form_details = await form_service.get_form_with_details(form_id)
        if not form_details:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Form not found"
            )
        logger.info(f"Comprehensive update completed successfully for form {form_id}")
        return form_details["form"]
    except ValueError as e:
        logger.error(f"Validation error in comprehensive update: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except DatabaseError as e:
        logger.error(f"Database error in comprehensive update: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error in comprehensive update: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        )


@router.post("/{form_id}/sections", response_model=Section, status_code=status.HTTP_201_CREATED)
@rbac_register(resource="form", action="create", group="Forms", description="Create new section")
async def create_section(
    form_id: str,
    request: SectionCreate,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Section:
    """Create a new section."""
    create_data = request.model_dump(exclude_unset=True)
    logger.info(f"Creating section with data: {create_data}")
    try:
        section = await form_service.create_section(
            form_id=form_id,
            create_data=create_data
        )
        return section
    except DatabaseError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.patch("/sections/{section_id}", response_model=Section)
@rbac_register(resource="form", action="edit", group="Forms", description="Update section")
async def update_section(
    section_id: str,
    request: SectionUpdate,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Section:
    """Update a section."""
    update_data = request.model_dump(exclude_unset=True)
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No update data provided"
        )

    section = await form_service.update_section(section_id, update_data)
    if not section:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Section not found"
        )
    return section


@router.put("/sections/{section_id}", response_model=Section)
@rbac_register(resource="form", action="edit", group="Forms", description="Update section (PUT)")
async def update_section_put(
    section_id: str,
    request: SectionUpdate,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Section:
    """Update a section using PUT method."""
    update_data = request.model_dump(exclude_unset=True)
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No update data provided"
        )

    section = await form_service.update_section(section_id, update_data)
    if not section:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Section not found"
        )
    return section


@router.delete("/sections/{section_id}", status_code=status.HTTP_204_NO_CONTENT)
@rbac_register(resource="form", action="delete", group="Forms", description="Delete section")
async def delete_section(
    section_id: str,
    form_service: FormServiceInterface = Depends(get_form_service)
):
    """Delete a section."""
    success = await form_service.delete_section(section_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Section not found"
        )


@router.post("/sections/{section_id}/questions", response_model=FormWithDetails, status_code=status.HTTP_201_CREATED)
@rbac_register(resource="form", action="create", group="Forms", description="Create new question")
async def create_question(
    section_id: str,
    request: QuestionCreate,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> FormWithDetails:
    """Create a new question and return the complete updated form."""
    create_data = request.model_dump(exclude_unset=True)
    try:
        form_with_details = await form_service.create_question(
            section_id=section_id,
            create_data=create_data
        )
        return form_with_details
    except DatabaseError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.patch("/questions/{question_id}", response_model=Question)
@rbac_register(resource="form", action="edit", group="Forms", description="Update question")
async def update_question(
    question_id: str,
    request: QuestionUpdate,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Question:
    """Update a question."""
    update_data = request.model_dump(exclude_unset=True)
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No update data provided"
        )

    logger.info(f"Updating question {question_id} with data: {update_data}")
    question = await form_service.update_question(question_id, update_data)
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )
    return question


@router.put("/questions/{question_id}", response_model=Question)
@rbac_register(resource="form", action="edit", group="Forms", description="Update question (PUT)")
async def update_question_put(
    question_id: str,
    request: QuestionUpdate,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Question:
    """Update a question using PUT method."""
    update_data = request.model_dump(exclude_unset=True)
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No update data provided"
        )

    logger.info(f"Updating question {question_id} with data (PUT): {update_data}")
    question = await form_service.update_question(question_id, update_data)
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )
    return question


@router.delete("/questions/{question_id}", status_code=status.HTTP_204_NO_CONTENT)
@rbac_register(resource="form", action="delete", group="Forms", description="Delete question")
async def delete_question(
    question_id: str,
    form_service: FormServiceInterface = Depends(get_form_service)
):
    """Delete a question."""
    success = await form_service.delete_question(question_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )


@router.api_route("/{form_id}/submit", methods=["POST", "PUT"], response_model=Submission)
@rbac_register(resource="form", action="submit", group="Forms", description="Submit or update form submission")
async def submit_form(
    form_id: str,
    request: Union[SubmitFormRequest, UpdateSubmissionRequest],
    http_request: Request,
    org_context: tuple[str, bool] = Depends(get_org_context),
    form_service: FormServiceInterface = Depends(get_form_service),
    queue_service = Depends(get_queue_service),
    job_service = Depends(get_job_service)
) -> Submission:
    """
    Submit a new form or update an existing submission.

    **POST /submit**: Creates a new submission
    **PUT /submit**: Updates an existing submission

    This endpoint supports two formats for answers:
    1. Flat structure (backward compatible): question_id -> answer
    2. Structured format with repeatable sections

    **POST Example** (create new submission):
    ```json
    {
      "answers": {
        "681e02498dddb89bb46eed76": "Acme Technologies",
        "681e02598dddb89bb46eed79": "https://acmetech.example.com"
      },
      "metadata": {
        "user_agent": "Mozilla/5.0",
        "ip_address": "127.0.0.1"
      }
    }
    ```

    **PUT Example** (update existing submission):
    ```json
    {
      "submission_id": "60f7b3b3b3b3b3b3b3b3b3b3",
      "answers": {
        "681e02498dddb89bb46eed76": "Updated Company Name"
      },
      "status": "draft",
      "metadata": {
        "last_saved": 1746799000
      }
    }
    ```

    For PUT requests, all fields are optional to support partial updates (autosave, status changes, etc.).
    """
    # Determine operation type
    is_update = http_request.method == "PUT"

    # Get the form to retrieve repeatable section information
    form_details = await form_service.get_form_with_details(form_id)
    if not form_details:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Form not found"
        )

    # Handle submission ID for updates
    submission_id = None
    if is_update:
        # For PUT requests, get submission_id from request body or query param
        if isinstance(request, UpdateSubmissionRequest):
            submission_id = request.submission_id

        if not submission_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="submission_id is required for PUT requests"
            )

        # Verify submission exists and belongs to this form
        existing_submission = await form_service.get_submission(submission_id)
        if not existing_submission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Submission not found"
            )

        if str(existing_submission.get("form_id")) != form_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Submission does not belong to this form"
            )

    # Process answers based on the provided format
    answers_to_validate = None
    answers_to_submit = None

    if hasattr(request, 'answer_schema') and request.answer_schema:
        # Use the structured answer schema for validation
        # Convert to dict for validation to avoid JSON serialization issues
        answers_to_validate = request.answer_schema.model_dump()
        # Use the structured format for submission
        answers_to_submit = answers_to_validate
    elif hasattr(request, 'answers') and request.answers:
        # Use the flat dictionary of answers (backward compatibility)
        answers_to_validate = request.answers
        answers_to_submit = request.answers
    elif not is_update:
        # For POST requests, answers are required
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No answers provided"
        )

    # Validate submission if answers are provided
    if answers_to_validate:
        validation = await form_service.validate_submission(form_id, answers_to_validate)
        if not validation["valid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=validation["errors"]
            )

    try:
        # Get organization ID from context
        org_id, _ = org_context

        if is_update:
            # Update existing submission
            metadata = getattr(request, 'metadata', None)
            status_update = getattr(request, 'status', None)

            submission = await form_service.update_submission(
                submission_id=submission_id,
                answers=answers_to_submit,
                metadata=metadata,
                status=status_update
            )

            if not submission:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Submission not found"
                )

            logger.info(f"Updated submission {submission_id}")
            return submission
        else:
            # Create new submission
            metadata = getattr(request, 'metadata', {})

            submission = await form_service.submit_form(
                form_id=form_id,
                answers=answers_to_submit,
                metadata=metadata
            )

            # Create a job chain for form processing and analysis
            jobs = await create_job_chain(
                entity_type="form_submission",
                entity_id=str(submission['id']),
                job_configs=[
                    # Form processing job
                    {
                        "job_type": "form_processing",
                        "queue_job_type": "process_form_submission",
                        "payload": {
                            "submission_id": str(submission['id']),
                            "form_id": form_id,
                            "org_id": org_id,
                            "answers": answers_to_submit  # Include answers for exclusion filter check
                        },
                        "metadata": {
                            "form_name": form_details.name,
                            "submission_time": int(datetime.now().timestamp())
                        }
                    }
                ]
            )
            logger.info(f"Created job chain for submission {submission['id']}: {jobs}")
            return submission

    except DatabaseError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/submissions/{submission_id}", response_model=Submission)
@rbac_register(resource="form", action="submit", group="Forms", description="Update submission by ID")
async def update_submission_by_id(
    submission_id: str,
    request: UpdateSubmissionRequest,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Submission:
    """
    Update an existing submission by its ID.

    This endpoint provides a more RESTful way to update submissions using the submission ID
    in the path rather than requiring it in the request body.

    Example payload:
    ```json
    {
      "answers": {
        "681e02498dddb89bb46eed76": "Updated Company Name"
      },
      "status": "draft",
      "metadata": {
        "last_saved": 1746799000,
        "auto_save": true
      }
    }
    ```

    All fields are optional to support partial updates (autosave, status changes, etc.).
    """
    try:
        # Verify submission exists
        existing_submission = await form_service.get_submission(submission_id)
        if not existing_submission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Submission not found"
            )

        # Process answers if provided
        answers_to_submit = None
        if hasattr(request, 'answer_schema') and request.answer_schema:
            answers_to_submit = request.answer_schema.model_dump()
        elif hasattr(request, 'answers') and request.answers:
            answers_to_submit = request.answers

        # Validate answers if provided
        if answers_to_submit:
            form_id = str(existing_submission.get("form_id"))
            validation = await form_service.validate_submission(form_id, answers_to_submit)
            if not validation["valid"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=validation["errors"]
                )

        # Update submission
        submission = await form_service.update_submission(
            submission_id=submission_id,
            answers=answers_to_submit,
            metadata=getattr(request, 'metadata', None),
            status=getattr(request, 'status', None)
        )

        if not submission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Submission not found"
            )

        logger.info(f"Updated submission {submission_id}")
        return submission

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating submission: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update submission: {str(e)}"
        )


@router.get("/submissions/{submission_id}", response_model=Submission)
@rbac_register(resource="form", action="view", group="Forms", description="View submission")
async def get_submission(
    submission_id: str,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Submission:
    """Get a submission by ID."""
    submission = await form_service.get_submission(submission_id)
    if not submission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Submission not found"
        )
    return submission


@router.get("/submissions/{submission_id}/jobs")
@rbac_register(resource="form", action="view", group="Forms", description="View submission jobs")
async def get_submission_jobs(
    submission_id: str,
    status: Optional[str] = Query(None, description="Filter by job status"),
    job_type: Optional[str] = Query(None, description="Filter by job type"),
    job_service = Depends(get_job_service)
):
    """
    Get all jobs for a form submission.

    Args:
        submission_id: Submission ID
        status: Optional filter by job status
        job_type: Optional filter by job type
        job_service: Job service instance

    Returns:
        List of jobs for the submission
    """
    try:
        jobs = await job_service.get_entity_jobs(
            entity_type="form_submission",
            entity_id=submission_id,
            status=status,
            job_type=job_type
        )

        return jobs
    except Exception as e:
        logger.error(f"Error retrieving submission jobs: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve submission jobs: {str(e)}"
        )


@router.get("/{form_id}/submissions", response_model=List[Submission])
@rbac_register(resource="form", action="view", group="Forms", description="View form submissions")
async def get_form_submissions(
    form_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    form_service: FormServiceInterface = Depends(get_form_service)
) -> List[Submission]:
    """Get all submissions for a form."""
    submissions = await form_service.get_form_submissions(form_id, skip, limit)
    return submissions


@router.put("/sections/{section_id}/questions/order", status_code=status.HTTP_204_NO_CONTENT)
@rbac_register(resource="form", action="edit", group="Forms", description="Update question order")
async def update_question_order(
    section_id: str,
    request: QuestionUpdate,
    form_service: FormServiceInterface = Depends(get_form_service)
):
    """Update the order of questions within a section."""
    update_data = request.model_dump(exclude_unset=True)
    try:
        success = await form_service.update_question_order(
            section_id,
            update_data
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update question order"
            )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except DatabaseError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/sections/{section_id}/details", response_model=SectionWithQuestions)
@rbac_register(resource="form", action="view", group="Forms", description="View section details")
async def get_section_details(
    section_id: str,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> SectionWithQuestions:
    """Get section with all its questions."""
    section = await form_service.get_section_with_questions(section_id)
    if not section:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Section not found"
        )
    return section


@router.get("/questions/{question_id}", response_model=Question)
@rbac_register(resource="form", action="view", group="Forms", description="View question")
async def get_question(
    question_id: str,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Question:
    """Get question by ID."""
    question = await form_service.get_question(question_id)
    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )
    return question


class FormEngineMode(str, Enum):
    """Mode for the form engine API."""
    SEQUENTIAL = "sequential"  # Step-by-step navigation
    FULL = "full"              # Full form structure with all logic
    NEXT = "next"              # Get only the next question


class FormEngineRequest(BaseModel):
    """Unified request model for the form engine API."""
    answers: Dict[str, Any] = Field(default_factory=dict, description="Current answers to form questions")
    mode: FormEngineMode = Field(default=FormEngineMode.SEQUENTIAL, description="Mode of operation")
    current_question_id: Optional[str] = Field(None, description="Current question ID (for NEXT mode)")
    include_dependencies: bool = Field(default=True, description="Include dependency metadata")
    include_validation: bool = Field(default=True, description="Include validation rules")
    include_visibility_rules: bool = Field(default=True, description="Include visibility conditions")


@router.post("/{form_id}/engine", response_model=Dict[str, Any])
@rbac_register(resource="form", action="view", group="Forms", description="Unified form engine API")
async def form_engine(
    form_id: str,
    request: FormEngineRequest,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Dict[str, Any]:
    """
    Unified form engine API that supports both sequential and full form modes.

    This endpoint provides a flexible interface for form rendering and navigation:

    - In SEQUENTIAL mode: Returns the form with sections and questions ordered based on
      dependencies, only including questions that are currently visible based on answers.

    - In FULL mode: Returns the entire form structure with all logic (visibility rules,
      conditions, dependencies) for visualization and rendering the complete form.

    - In NEXT mode: Returns only the next question based on the current question and answers,
      supporting step-by-step navigation.

    All modes properly handle repeatable sections, with appropriate scoping and instance
    management based on numeric answers.

    The response is structured to be UI-friendly, making it easy to build visual maps
    of the form flow, showing connections, conditional branches, and dependencies.
    """
    try:
        # Use the unified form engine service method
        result = await form_service.get_form_engine_data(
            form_id=form_id,
            answers=request.answers,
            mode=request.mode,
            current_question_id=request.current_question_id,
            include_dependencies=request.include_dependencies,
            include_validation=request.include_validation,
            include_visibility_rules=request.include_visibility_rules
        )
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process form engine request: {str(e)}"
        )


# Keep the original endpoints for backward compatibility
@router.post("/{form_id}/visible-questions", response_model=List[Dict[str, Any]])
@rbac_register(resource="form", action="view", group="Forms", description="Get visible questions based on current answers")
async def get_visible_questions(
    form_id: str,
    request: VisibleQuestionsRequest,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> List[Dict[str, Any]]:
    """
    Get all visible questions based on current answers.

    This endpoint evaluates visibility conditions and returns only the questions
    that should be visible based on the provided answers. It also handles
    repeatable sections by including the appropriate number of instances.

    Note: Consider using the unified /engine endpoint instead for more flexibility.
    """
    try:
        visible_questions = await form_service.get_visible_questions(form_id, request.answers)
        return visible_questions
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get visible questions: {str(e)}"
        )


@router.post("/{form_id}/sequential", response_model=Dict[str, Any])
@rbac_register(resource="form", action="view", group="Forms", description="Get form in sequential format")
async def get_sequential_form(
    form_id: str,
    request: SequentialFormRequest,
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Dict[str, Any]:
    """
    Get form in a sequential format for filling.

    Returns the form with sections and questions ordered based on dependencies,
    and only includes questions that are currently visible based on answers.

    This endpoint is optimized for form filling interfaces, providing:
    - Only currently visible questions based on answers
    - Questions ordered by dependencies (questions that control visibility of others appear first)
    - Metadata about dependencies for frontend rendering
    - Proper handling of repeatable sections

    DEPRECATED: Please use the unified /engine endpoint with mode="sequential" instead for more
    flexibility and consistent behavior. This endpoint will be removed in a future version.
    """
    try:
        # Use the unified form engine API with sequential mode
        return await form_service.get_form_engine_data(
            form_id=form_id,
            answers=request.answers,
            mode="sequential"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get sequential form: {str(e)}"
        )


@router.post("/sections/reorder", status_code=status.HTTP_200_OK)
@rbac_register(resource="form", action="edit", group="Forms", description="Bulk reorder sections")
async def reorder_sections(
    request: ReorderRequest,
    form_service: FormServiceInterface = Depends(get_form_service)):
    """
    Bulk reorder sections atomically.

    Validates that:
    1. All section IDs exist
    2. No duplicate orders
    3. All sections from the form are included
    4. Updates are applied atomically
    """
    try:
        success = await form_service.reorder_sections(request.items)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to reorder sections"
            )
        return {"status": "ok"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except DatabaseError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error occurred while reordering sections: {str(e)}"
        )

@router.post("/questions/reorder", status_code=status.HTTP_200_OK)
@rbac_register(resource="form", action="edit", group="Forms", description="Bulk reorder questions")
async def reorder_questions(
    request: ReorderRequest,
    form_service: FormServiceInterface = Depends(get_form_service)):
    """
    Bulk reorder questions atomically.

    Validates that:
    1. All question IDs exist
    2. No duplicate orders
    3. All questions from the section are included
    4. Updates are applied atomically
    """
    try:
        success = await form_service.reorder_questions(request.items)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to reorder questions"
            )
        return {"status": "ok"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except DatabaseError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error occurred while reordering questions: {str(e)}"
        )


# Public endpoints for shared forms (no authentication required)

class SharedFormDetailsResponse(TractionXModel):
    """Response model for shared form details."""
    form: FormWithDetails
    organization: Dict[str, Any]
    sharing_config: Dict[str, Any]
    branding: Optional[Dict[str, Any]] = None


class SharedSubmissionRequest(TractionXModel):
    """Request model for submitting a shared form."""
    answers: Dict[str, Any] = Field(..., description="Form answers")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Optional metadata")
    user_email: Optional[str] = Field(default=None, description="Optional user email for tracking")


class ProgressSaveRequest(TractionXModel):
    """Request model for saving form progress."""
    answers: Dict[str, Any] = Field(..., description="Current form answers")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Optional metadata")


@public_router.get("/share/{token}/details", response_model=SharedFormDetailsResponse)
async def get_shared_form_details(
    token: str,
    sharing_service: SharingServiceInterface = Depends(get_sharing_service),
    form_service: FormServiceInterface = Depends(get_form_service)
) -> SharedFormDetailsResponse:
    """
    Get shared form details by token (public endpoint).

    Returns form schema, organization branding, and sharing configuration.
    """
    try:
        # Validate sharing token
        sharing_link = await SharingLink.find_one(query={
            "token": token
        })
        logger.info(f"sharing_link: {sharing_link}")

        if not sharing_link:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invalid or expired sharing link"
            )
        sharing_config = await SharingConfig.find_one(query={
            "_id": ObjectId(sharing_link.sharing_config_id)
        })
        # Validate sharing token
        sharing_data = await sharing_service.validate_sharing_token(config=sharing_config)

        if not sharing_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invalid or expired sharing link"
            )

        # Extract form and organization data
        form_data = sharing_data.get("resource_data")
        org_data = sharing_data.get("organization")
        config_data = sharing_data.get("sharing_config")

        if not form_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Form not found"
            )

        # Get detailed form data with sections and questions
        form_details = await form_service.get_form_with_details(str(form_data["_id"]))
        if not form_details:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Form details not found"
            )

        # Track the view
        await sharing_service.track_view(
            sharing_id=config_data.get("_id"),
            sharing_type=SharingType.LINK,
            metadata={"user_agent": "web", "source": "share_page"}
        )
        
        logger.info(f"form_details: {form_details}")

        return SharedFormDetailsResponse(
            form=form_details,
            organization=org_data or {},
            sharing_config=config_data or {},
            branding=config_data.get("custom_styles") if config_data else None
        ).model_dump(by_alias=True)

    except Exception as e:
        logger.error(f"Error getting shared form details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load form details"
        )


@public_router.post("/share/{token}/submit", response_model=Submission, status_code=status.HTTP_201_CREATED)
async def submit_shared_form(
    token: str,
    request: SharedSubmissionRequest,
    sharing_service: SharingServiceInterface = Depends(get_sharing_service),
    form_service: FormServiceInterface = Depends(get_form_service)
) -> Submission:
    """
    Submit a shared form by token (public endpoint).
    """
    try:
        # Validate sharing token
        sharing_link = await SharingLink.find_one(query={
            "token": token
        })
        logger.info(f"sharing_link: {sharing_link}")

        if not sharing_link:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invalid or expired sharing link"
            )

        sharing_config = await SharingConfig.find_one(query={
            "_id": ObjectId(sharing_link.sharing_config_id)     
        })
        sharing_data = await sharing_service.validate_sharing_token(config=sharing_config)
        if not sharing_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invalid or expired sharing link"
            )

        # Extract form and organization data
        form_data = sharing_data.get("resource_data")
        org_data = sharing_data.get("organization")

        if not form_data or not org_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Form or organization not found"
            )

        form_id = str(form_data["_id"])
        org_id = str(org_data["_id"])
        logger.info(f"form_id: {form_id}, org_id: {org_id}")

        # Create submission with sharing context
        submission_metadata = {
            **(request.metadata or {}),
            "sharing_token": token,
            "submission_source": "shared_form",
            "user_email": request.user_email
        }

        # Submit the form
        submission = await form_service.submit_form(
            form_id=form_id,
            answers=request.answers,
            metadata=submission_metadata
        )
        logger.info(f"submission: {submission}")
        # lets create a deal for the submission and store the submission id in the deal and vice versa
        form_details = await form_service.get_form_with_details(form_id)
        deal = Deal(
            form_id=ObjectId(form_id),
            org_id=ObjectId(org_id),
            submission_ids=[ObjectId(submission['_id'])],
        )
        logger.info(f'Form Id: {form_id}, Org Id: {org_id}, Submission Id: {submission["_id"]}')
        # Also map the company name from the submission to the deal
        
        logger.info(f"form_details: {form_details}")
        
        # Extract all questions from all sections
        all_questions = []
        for section in form_details.sections:
            all_questions.extend(section.questions)
        
        logger.info(f"all_questions count: {len(all_questions)}")
        
        # Map core fields from answers
        core_questions = {}
        for question in all_questions:
            logger.info(f"Processing question: id={str(question.id)}, core_field={getattr(question, 'core_field', None)}")
            if hasattr(question, 'core_field') and question.core_field:
                question_id_str = str(question.id)
                answer_value = request.answers.get(question_id_str)
                logger.info(f"Core field mapping: {question.core_field} = {answer_value} (from question {question_id_str})")
                core_questions[question.core_field] = answer_value
        
        logger.info(f"core_questions: {core_questions}")
        
        deal.company_name = core_questions.get("company_name")
        deal.stage = core_questions.get("stage")
        deal.sector = core_questions.get("sector")
        logger.info(f"Deal : {deal}")
        await deal.save()
        
        jobs = await create_job_chain(
                entity_type="form_submission",
                entity_id=str(submission['_id']),
                job_configs=[
                    # Form processing job
                    {
                        "job_type": "form_processing",
                        "queue_job_type": "process_form_submission",
                        "payload": {
                            "submission_id": str(submission['_id']),
                            "form_id": form_id,
                            "org_id": org_id,
                            "answers": request.answers  # Include answers for exclusion filter check
                        },
                        "metadata": {
                            "form_name": form_details.name,
                            "submission_time": int(datetime.now().timestamp())
                        }
                    }
                ]
            )
        logger.info(f"jobs: {jobs}")
        logger.info(f"deal: {deal}")
        logger.info(f"Shared form submission created: {submission}")
        return submission

    except Exception as e:
        logger.error(f"Error submitting shared form: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit form"
        )
