"""
File models for TractionX submission file handling.

This module defines models for tracking files uploaded as part of form submissions,
including metadata, S3 storage information, and access control.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any
from enum import Enum

from pydantic import Field

from app.core.database import Traction<PERSON>Model, ObjectIdField


class FileStatus(str, Enum):
    """File status enumeration."""
    UPLOADING = "uploading"
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    READY = "ready"
    ERROR = "error"
    DELETED = "deleted"


class FileAccessType(str, Enum):
    """File access type enumeration."""
    PUBLIC_USER = "public_user"  # Uploaded by public user via shared form
    ORGANIZATION_USER = "organization_user"  # Uploaded by org user
    INVESTOR = "investor"  # Accessed by investor


class SubmissionFile(TractionXModel):
    """
    Represents a file uploaded as part of a form submission.
    
    This model tracks files uploaded through shared forms by public users
    or through regular forms by organization users. Files are stored in S3
    and access is controlled through presigned URLs.
    
    Key features:
    - S3 storage with presigned URL access
    - Tied to specific submission and question
    - User access control and audit trail
    - File validation and metadata tracking
    """
    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")
    
    # Core relationships
    submission_id: ObjectIdField = Field(..., description="ID of the submission this file belongs to")
    question_id: ObjectIdField = Field(..., description="ID of the question this file answers")
    org_id: ObjectIdField = Field(..., description="Organization ID for access control")
    
    # User tracking
    uploaded_by_user_id: ObjectIdField = Field(..., description="ID of the user who uploaded the file")
    uploaded_by_email: str = Field(..., description="Email of the user who uploaded the file")
    access_type: FileAccessType = Field(..., description="Type of user access (public_user, organization_user, investor)")
    
    # File metadata
    original_filename: str = Field(..., description="Original filename as uploaded")
    s3_key: str = Field(..., description="S3 object key for the file")
    s3_bucket: str = Field(..., description="S3 bucket name")
    mime_type: str = Field(..., description="MIME type of the file")
    file_size: int = Field(..., description="File size in bytes")
    
    # File status and processing
    status: FileStatus = Field(default=FileStatus.UPLOADING, description="Current status of the file")
    upload_completed_at: Optional[int] = Field(default=None, description="Timestamp when upload was completed")
    
    # Validation and security
    checksum: Optional[str] = Field(default=None, description="File checksum for integrity verification")
    virus_scan_status: Optional[str] = Field(default=None, description="Virus scan status (clean, infected, pending)")
    
    # Access tracking
    download_count: int = Field(default=0, description="Number of times file has been downloaded")
    last_accessed_at: Optional[int] = Field(default=None, description="Last time file was accessed")
    last_accessed_by: Optional[str] = Field(default=None, description="Email of last user to access file")
    
    # Metadata and configuration
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional file metadata")
    is_active: bool = Field(default=True, description="Whether the file is active (not soft-deleted)")
    
    # Timestamps
    created_at: int = Field(default_factory=lambda: int(datetime.now(timezone.utc).timestamp()))
    updated_at: int = Field(default_factory=lambda: int(datetime.now(timezone.utc).timestamp()))
    
    def mark_uploaded(self) -> None:
        """Mark the file as successfully uploaded."""
        self.status = FileStatus.UPLOADED
        self.upload_completed_at = int(datetime.now(timezone.utc).timestamp())
        self.updated_at = int(datetime.now(timezone.utc).timestamp())
    
    def mark_ready(self) -> None:
        """Mark the file as ready for access."""
        self.status = FileStatus.READY
        self.updated_at = int(datetime.now(timezone.utc).timestamp())
    
    def mark_error(self, error_message: str = None) -> None:
        """Mark the file as having an error."""
        self.status = FileStatus.ERROR
        if error_message:
            self.metadata["error_message"] = error_message
        self.updated_at = int(datetime.now(timezone.utc).timestamp())
    
    def soft_delete(self) -> None:
        """Soft delete the file."""
        self.is_active = False
        self.status = FileStatus.DELETED
        self.updated_at = int(datetime.now(timezone.utc).timestamp())
    
    def track_access(self, accessed_by_email: str) -> None:
        """Track file access."""
        self.download_count += 1
        self.last_accessed_at = int(datetime.now(timezone.utc).timestamp())
        self.last_accessed_by = accessed_by_email
        self.updated_at = int(datetime.now(timezone.utc).timestamp())
    
    def can_be_accessed_by(self, user_email: str, user_type: str, submission_status: str = None) -> bool:
        """
        Check if a user can access this file.
        
        Args:
            user_email: Email of the user requesting access
            user_type: Type of user (public_user, organization_user, investor)
            submission_status: Status of the submission (draft, submitted)
        
        Returns:
            bool: True if user can access the file
        """
        if not self.is_active or self.status == FileStatus.DELETED:
            return False
        
        # File must be ready for access
        if self.status not in [FileStatus.UPLOADED, FileStatus.READY]:
            return False
        
        # Original uploader can always access their files
        if self.uploaded_by_email == user_email:
            return True
        
        # Investors can only access files from submitted submissions
        if user_type == "investor":
            return submission_status == "submitted"
        
        # Organization users can access files within their org
        if user_type == "organization_user":
            return True
        
        return False
    
    @property
    def file_extension(self) -> str:
        """Get file extension from original filename."""
        if "." in self.original_filename:
            return self.original_filename.split(".")[-1].lower()
        return ""
    
    @property
    def display_size(self) -> str:
        """Get human-readable file size."""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} KB"
        elif self.file_size < 1024 * 1024 * 1024:
            return f"{self.file_size / (1024 * 1024):.1f} MB"
        else:
            return f"{self.file_size / (1024 * 1024 * 1024):.1f} GB"


class FileAccessLog(TractionXModel):
    """
    Audit log for file access events.
    
    Tracks all file access attempts for security and compliance.
    """
    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")
    
    # File and user information
    file_id: ObjectIdField = Field(..., description="ID of the file accessed")
    submission_id: ObjectIdField = Field(..., description="ID of the submission")
    org_id: ObjectIdField = Field(..., description="Organization ID")
    
    # Access details
    accessed_by_email: str = Field(..., description="Email of user who accessed the file")
    access_type: FileAccessType = Field(..., description="Type of access")
    action: str = Field(..., description="Action performed (download, view, delete)")
    
    # Request metadata
    ip_address: Optional[str] = Field(default=None, description="IP address of the request")
    user_agent: Optional[str] = Field(default=None, description="User agent string")
    
    # Result
    success: bool = Field(..., description="Whether the access was successful")
    error_message: Optional[str] = Field(default=None, description="Error message if access failed")
    
    # Timestamp
    created_at: int = Field(default_factory=lambda: int(datetime.now(timezone.utc).timestamp()))
